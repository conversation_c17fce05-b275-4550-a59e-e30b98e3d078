<!DOCTYPE html>
<html lang="en">


<!-- Mirrored from www.ictministry.gov.zw/pages/projects by HTTrack Website Copier/3.x [XR&CO'2014], Thu, 26 Jun 2025 08:58:10 GMT -->
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Official website of Zimbabwe's Ministry of ICT, Postal & Courier Services" />
    <title>Ministry of ICT Zimbabwe - Official Website</title>
    <link rel="icon" href="../icon.png" type="image/png" />
    <link href="../../cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet" />
    <script src="https://unpkg.com/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&amp;display=swap"
        rel="stylesheet" />

    <!-- Tailwind CSS -->
    <script src="../../cdnjs.cloudflare.com/ajax/libs/alpinejs/3.13.5/cdn.js"></script>
    <script src="../../cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.1/flowbite.min.js"></script>
    <script src="../../cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/js/all.min.js"></script>
    <link href="../../cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.1/flowbite.min.css" rel="stylesheet" />
    <link href="../../cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet" />
    <script src="../../cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>
    <link rel="stylesheet" href="../../cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css">
    <script src="../../cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>


    <!--Custom CSS-->
    <link rel="stylesheet" href="../src/style.css" />
    <link rel="stylesheet" href="../src/projects.html" />

    <!-- Gallery Custom Styles -->
    <style>
        .gallery-grid {
            perspective: 1000px;
        }

        .gallery-item {
            transform-style: preserve-3d;
            transition: all 0.3s ease;
        }

        .gallery-item:hover {
            transform: translateY(-5px);
        }

        .gallery-filter-btn {
            position: relative;
            overflow: hidden;
        }

        .gallery-filter-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .gallery-filter-btn:hover::before {
            left: 100%;
        }

        .lightbox-modal {
            backdrop-filter: blur(10px);
        }

        @media (max-width: 768px) {
            .gallery-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }
        }

        @media (max-width: 480px) {
            .gallery-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>

</head>
 

    <!-- Navbar -->
    <header>
        <nav x-data="{ isOpen: false }" class="bg-white border-b border-gray-200 fixed w-full z-50 text-md ">
            <div class="max-w-7xl mx-auto px-2 sm:px-6 lg:px-6 text-2xl">
                <div class="flex justify-between items-center h-20">
                    <div class="flex-none mr-auto">
                        <img src="../ZCC PNG.png" alt="Ministry of ICT Zimbabwe Logo" class="h-16 w-auto" />
                    </div>
    
                    <div class="hidden md:flex md:items-center md:justify-end flex-1">
                        <div class="flex space-x-6">
                            <a href="../index-2.html"
                                class="text-[#244A64] hover:text-[#037F06] px-3 py-6 text-sm font-medium transition-colors"
                                aria-current="page">Home</a>
                            <div class="relative group">
                                <a href="about.html">
                                    <button
                                        class="text-[#244A64] hover:text-[#037F06] px-3 py-6 text-sm font-medium transition-colors">
                                        About the Project
                                    </button>
                                </a>
                                
                            </div>
                            <a href="resource.html"
                                class="text-[#244A64] hover:text-[#037F06] px-3 py-6 text-sm font-medium transition-colors">Our Resources</a>
                            <a href="projects.html"
                                class="text-[#244A64] hover:text-[#037F06] px-3 py-6 text-sm font-medium transition-colors">Gallery</a>
                            <a href="news.html"
                                class="text-[#244A64] hover:text-[#037F06] px-3 py-6 text-sm font-medium transition-colors">News
                                and Updates</a>
                            <a href="contact.html"
                                class="text-[#244A64] hover:text-[#037F06] px-3 py-6 text-sm font-medium transition-colors">Contact</a>
                        </div>
                    </div>
    
                    <!-- Mobile menu button -->
                    <div class="md:hidden">
                        <button @click="isOpen = !isOpen" class="text-[#244A64]" aria-label="Toggle menu">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path x-show="!isOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 6h16M4 12h16M4 18h16" />
                                <path x-show="isOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
    
            <!-- Mobile menu -->
            <div x-show="isOpen" class="md:hidden bg-white border-b border-gray-200">
                <div class="px-2 pt-2 pb-3 space-y-1">
                    <a href="../index-2.html" class="block px-3 py-2 text-[#244A64] hover:text-[#037F06] font-medium">Home</a>
                    <a href="about.html" class="block px-3 py-2 text-[#244A64] hover:text-[#037F06] font-medium">Who
                        We Are</a>
                    <a href="resource.html"
                        class="block px-3 py-2 text-[#244A64] hover:text-[#037F06] font-medium">Resources</a>
                    <a href="projects.html"
                        class="block px-3 py-2 text-[#244A64] hover:text-[#037F06] font-medium">Projects</a>
                    <a href="news.html" class="block px-3 py-2 text-[#244A64] hover:text-[#037F06] font-medium">News
                        and Updates</a>
                    <a href="contact.html"
                        class="block px-3 py-2 text-[#244A64] hover:text-[#037F06] font-medium">Contact</a>
                </div>
            </div>
        </nav>
    </header>


<body>
        <!-- Hero Section -->
        <section class="relative h-[65vh] flex items-center justify-center overflow-hidden mb-8">
    <div class="absolute inset-0">
        <img src="../assets/images/farm2.jpg" alt="Digital Zimbabwe" class="w-full h-full object-cover opacity-90 mt-16">
        <div class="absolute inset-0 bg-black opacity-50"></div>
    </div>
    <div class="relative z-10 text-center px-4" data-aos="fade-up">
        <h1 class="text-4xl md:text-6xl font-bold text-white mb-4">Our Gallery</h1>
        <p class="text-xl text-gray-200 max-w-3xl mx-auto">Scaling Up Livelihoods & Resillience</p>
    </div>
</section>

    
        <!-- Featured Video Section -->
        <section class="container mx-auto px-4 mb-16">
            <div class="text-center mb-12" data-aos="fade-up">
                <h2 class="text-3xl font-bold text-[#244A64] mb-4">Featured Content</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Watch our latest videos showcasing the impact of the Scaling Up Resilience Program</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" data-aos="fade-up">
                <!-- Main Featured Video -->
                <div class="md:col-span-2 lg:col-span-2">
                    <div class="relative overflow-hidden rounded-xl shadow-2xl h-80 group">
                        <iframe
                            src="https://www.youtube.com/embed/JYoCiWdNn8w?autoplay=0&mute=1&controls=1"
                            title="Scaling Up Resilience Program Overview"
                            class="w-full h-full"
                            frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowfullscreen
                        ></iframe>
                        <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6">
                            <h3 class="text-white text-xl font-semibold mb-2">Program Overview</h3>
                            <p class="text-gray-200 text-sm">Learn about our comprehensive approach to building resilience in rural communities</p>
                        </div>
                    </div>
                </div>

                <!-- Secondary Videos -->
                <div class="space-y-6">
                    <div class="relative overflow-hidden rounded-xl shadow-lg h-36 group">
                        <iframe
                            src="https://www.youtube.com/embed/JYoCiWdNn8w?autoplay=0&mute=1&controls=1"
                            title="Climate Smart Agriculture"
                            class="w-full h-full"
                            frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowfullscreen
                        ></iframe>
                        <div class="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-all duration-300"></div>
                    </div>

                    <div class="relative overflow-hidden rounded-xl shadow-lg h-36 group">
                        <iframe
                            src="https://www.youtube.com/embed/JYoCiWdNn8w?autoplay=0&mute=1&controls=1"
                            title="Community Success Stories"
                            class="w-full h-full"
                            frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowfullscreen
                        ></iframe>
                        <div class="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-all duration-300"></div>
                    </div>
                </div>
            </div>
        </section>
    
        <!-- Gallery Filter Section -->
        <section class="container mx-auto px-4 py-12">
            <div class="text-center mb-12" data-aos="fade-up">
                <h2 class="text-4xl font-bold text-[#244A64] mb-4">Photo Gallery</h2>
                <p class="text-gray-600 max-w-3xl mx-auto mb-8">Explore the visual journey of our Scaling Up Resilience Program through these impactful moments captured across communities in Gutu and Bikita districts.</p>

                <!-- Gallery Statistics -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
                    <div class="text-center p-4 bg-white rounded-lg shadow-md">
                        <div class="text-2xl font-bold text-[#037F06]" id="total-photos">16</div>
                        <div class="text-gray-600 text-sm">Total Photos</div>
                    </div>
                    <div class="text-center p-4 bg-white rounded-lg shadow-md">
                        <div class="text-2xl font-bold text-[#244A64]" id="agriculture-count">6</div>
                        <div class="text-gray-600 text-sm">Agriculture</div>
                    </div>
                    <div class="text-center p-4 bg-white rounded-lg shadow-md">
                        <div class="text-2xl font-bold text-[#CFB01A]" id="isals-count">3</div>
                        <div class="text-gray-600 text-sm">ISALs</div>
                    </div>
                    <div class="text-center p-4 bg-white rounded-lg shadow-md">
                        <div class="text-2xl font-bold text-[#037F06]" id="stories-count">7</div>
                        <div class="text-gray-600 text-sm">Stories</div>
                    </div>
                </div>

                <!-- Filter Buttons -->
                <div class="flex flex-wrap justify-center gap-4 mb-8">
                    <button class="gallery-filter-btn active px-6 py-3 rounded-full bg-[#037F06] text-white font-medium transition-all duration-300 hover:bg-[#244A64] shadow-lg" data-filter="all">
                        <i class="fas fa-images mr-2"></i>All Photos
                    </button>
                    <button class="gallery-filter-btn px-6 py-3 rounded-full bg-gray-200 text-gray-700 font-medium transition-all duration-300 hover:bg-[#037F06] hover:text-white shadow-lg" data-filter="agriculture">
                        <i class="fas fa-seedling mr-2"></i>Climate Smart Agriculture
                    </button>
                    <button class="gallery-filter-btn px-6 py-3 rounded-full bg-gray-200 text-gray-700 font-medium transition-all duration-300 hover:bg-[#037F06] hover:text-white shadow-lg" data-filter="isals">
                        <i class="fas fa-coins mr-2"></i>ISALs & Livelihoods
                    </button>
                    <button class="gallery-filter-btn px-6 py-3 rounded-full bg-gray-200 text-gray-700 font-medium transition-all duration-300 hover:bg-[#037F06] hover:text-white shadow-lg" data-filter="gender">
                        <i class="fas fa-users mr-2"></i>Gender & SGBV
                    </button>
                    <button class="gallery-filter-btn px-6 py-3 rounded-full bg-gray-200 text-gray-700 font-medium transition-all duration-300 hover:bg-[#037F06] hover:text-white shadow-lg" data-filter="success">
                        <i class="fas fa-trophy mr-2"></i>Success Stories
                    </button>
                </div>
            </div>

            <!-- Gallery Grid -->
            <div class="gallery-grid grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6" data-aos="fade-up">

                <!-- Climate Smart Agriculture Images -->
                <div class="gallery-item agriculture group cursor-pointer" data-category="agriculture">
                    <div class="relative overflow-hidden rounded-xl shadow-lg aspect-square">
                        <img src="../assets/images/agric1.jpg" alt="Climate Smart Agriculture - Demonstration Plot" class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110" loading="lazy">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-4 left-4 right-4">
                                <h3 class="text-white font-semibold text-lg mb-1">Demonstration Plot</h3>
                                <p class="text-gray-200 text-sm">Farmers learning climate-smart techniques</p>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="bg-white/20 backdrop-blur-sm rounded-full p-2">
                                <i class="fas fa-expand text-white text-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="gallery-item agriculture group cursor-pointer" data-category="agriculture">
                    <div class="relative overflow-hidden rounded-xl shadow-lg aspect-square">
                        <img src="../assets/images/agric2.jpg" alt="Climate Smart Agriculture - Crop Management" class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110" loading="lazy">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-4 left-4 right-4">
                                <h3 class="text-white font-semibold text-lg mb-1">Crop Management</h3>
                                <p class="text-gray-200 text-sm">Sustainable farming practices in action</p>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="bg-white/20 backdrop-blur-sm rounded-full p-2">
                                <i class="fas fa-expand text-white text-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="gallery-item agriculture group cursor-pointer" data-category="agriculture">
                    <div class="relative overflow-hidden rounded-xl shadow-lg aspect-square">
                        <img src="../assets/images/agric3.jpg" alt="Climate Smart Agriculture - Training Session" class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110" loading="lazy">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-4 left-4 right-4">
                                <h3 class="text-white font-semibold text-lg mb-1">Training Session</h3>
                                <p class="text-gray-200 text-sm">Community members learning new techniques</p>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="bg-white/20 backdrop-blur-sm rounded-full p-2">
                                <i class="fas fa-expand text-white text-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="gallery-item agriculture group cursor-pointer" data-category="agriculture">
                    <div class="relative overflow-hidden rounded-xl shadow-lg aspect-square">
                        <img src="../assets/images/agric4.jpg" alt="Climate Smart Agriculture - Harvest Time" class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110" loading="lazy">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-4 left-4 right-4">
                                <h3 class="text-white font-semibold text-lg mb-1">Harvest Time</h3>
                                <p class="text-gray-200 text-sm">Celebrating successful harvests</p>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="bg-white/20 backdrop-blur-sm rounded-full p-2">
                                <i class="fas fa-expand text-white text-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="gallery-item agriculture group cursor-pointer" data-category="agriculture">
                    <div class="relative overflow-hidden rounded-xl shadow-lg aspect-square">
                        <img src="../assets/images/agric5.jpg" alt="Climate Smart Agriculture - Field Work" class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-4 left-4 right-4">
                                <h3 class="text-white font-semibold text-lg mb-1">Field Work</h3>
                                <p class="text-gray-200 text-sm">Hands-on agricultural activities</p>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="bg-white/20 backdrop-blur-sm rounded-full p-2">
                                <i class="fas fa-expand text-white text-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="gallery-item agriculture group cursor-pointer" data-category="agriculture">
                    <div class="relative overflow-hidden rounded-xl shadow-lg aspect-square">
                        <img src="../assets/images/agric6.jpg" alt="Climate Smart Agriculture - Community Garden" class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-4 left-4 right-4">
                                <h3 class="text-white font-semibold text-lg mb-1">Community Garden</h3>
                                <p class="text-gray-200 text-sm">Collaborative farming initiatives</p>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="bg-white/20 backdrop-blur-sm rounded-full p-2">
                                <i class="fas fa-expand text-white text-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- ISALs & Livelihoods Images -->
                <div class="gallery-item isals group cursor-pointer" data-category="isals">
                    <div class="relative overflow-hidden rounded-xl shadow-lg aspect-square">
                        <img src="../assets/images/isals.jpg" alt="ISALs - Savings Group Meeting" class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-4 left-4 right-4">
                                <h3 class="text-white font-semibold text-lg mb-1">Savings Group Meeting</h3>
                                <p class="text-gray-200 text-sm">Community members participating in ISALs</p>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="bg-white/20 backdrop-blur-sm rounded-full p-2">
                                <i class="fas fa-expand text-white text-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="gallery-item isals group cursor-pointer" data-category="isals">
                    <div class="relative overflow-hidden rounded-xl shadow-lg aspect-square">
                        <img src="../assets/images/isals1.jpg" alt="ISALs - Financial Literacy Training" class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-4 left-4 right-4">
                                <h3 class="text-white font-semibold text-lg mb-1">Financial Literacy</h3>
                                <p class="text-gray-200 text-sm">Training on financial management</p>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="bg-white/20 backdrop-blur-sm rounded-full p-2">
                                <i class="fas fa-expand text-white text-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="gallery-item isals group cursor-pointer" data-category="isals">
                    <div class="relative overflow-hidden rounded-xl shadow-lg aspect-square">
                        <img src="../assets/images/isals2.jpg" alt="ISALs - Business Planning" class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-4 left-4 right-4">
                                <h3 class="text-white font-semibold text-lg mb-1">Business Planning</h3>
                                <p class="text-gray-200 text-sm">Developing sustainable business plans</p>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="bg-white/20 backdrop-blur-sm rounded-full p-2">
                                <i class="fas fa-expand text-white text-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Gender & SGBV Images -->
                <div class="gallery-item gender group cursor-pointer" data-category="gender">
                    <div class="relative overflow-hidden rounded-xl shadow-lg aspect-square">
                        <img src="../assets/images/gender.jpg" alt="Gender - Community Dialogue" class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-4 left-4 right-4">
                                <h3 class="text-white font-semibold text-lg mb-1">Community Dialogue</h3>
                                <p class="text-gray-200 text-sm">Gender equality discussions</p>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="bg-white/20 backdrop-blur-sm rounded-full p-2">
                                <i class="fas fa-expand text-white text-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="gallery-item gender group cursor-pointer" data-category="gender">
                    <div class="relative overflow-hidden rounded-xl shadow-lg aspect-square">
                        <img src="../assets/images/gender2.jpg" alt="Gender - Women Empowerment" class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-4 left-4 right-4">
                                <h3 class="text-white font-semibold text-lg mb-1">Women Empowerment</h3>
                                <p class="text-gray-200 text-sm">Supporting women's leadership roles</p>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="bg-white/20 backdrop-blur-sm rounded-full p-2">
                                <i class="fas fa-expand text-white text-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Success Stories Images -->
                <div class="gallery-item success group cursor-pointer" data-category="success">
                    <div class="relative overflow-hidden rounded-xl shadow-lg aspect-square">
                        <img src="../assets/images/successstories.jpg" alt="Success Story - Hedwick's Journey" class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-4 left-4 right-4">
                                <h3 class="text-white font-semibold text-lg mb-1">Hedwick's Journey</h3>
                                <p class="text-gray-200 text-sm">From survival to self-reliance</p>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="bg-white/20 backdrop-blur-sm rounded-full p-2">
                                <i class="fas fa-expand text-white text-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="gallery-item success group cursor-pointer" data-category="success">
                    <div class="relative overflow-hidden rounded-xl shadow-lg aspect-square">
                        <img src="../assets/images/successstories2.jpg" alt="Success Story - Harvest Celebration" class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-4 left-4 right-4">
                                <h3 class="text-white font-semibold text-lg mb-1">Harvest Celebration</h3>
                                <p class="text-gray-200 text-sm">A harvest worth celebrating</p>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="bg-white/20 backdrop-blur-sm rounded-full p-2">
                                <i class="fas fa-expand text-white text-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="gallery-item success group cursor-pointer" data-category="success">
                    <div class="relative overflow-hidden rounded-xl shadow-lg aspect-square">
                        <img src="../assets/images/successstories3.jpg" alt="Success Story - Community Transformation" class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-4 left-4 right-4">
                                <h3 class="text-white font-semibold text-lg mb-1">Community Transformation</h3>
                                <p class="text-gray-200 text-sm">Justice and dignity in action</p>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="bg-white/20 backdrop-blur-sm rounded-full p-2">
                                <i class="fas fa-expand text-white text-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="gallery-item success group cursor-pointer" data-category="success">
                    <div class="relative overflow-hidden rounded-xl shadow-lg aspect-square">
                        <img src="../assets/images/successstories4.jpg" alt="Success Story - Youth Empowerment" class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-4 left-4 right-4">
                                <h3 class="text-white font-semibold text-lg mb-1">Youth Empowerment</h3>
                                <p class="text-gray-200 text-sm">Young farmers leading change</p>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="bg-white/20 backdrop-blur-sm rounded-full p-2">
                                <i class="fas fa-expand text-white text-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="gallery-item success group cursor-pointer" data-category="success">
                    <div class="relative overflow-hidden rounded-xl shadow-lg aspect-square">
                        <img src="../assets/images/successstories5.jpg" alt="Success Story - Resilient Communities" class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-4 left-4 right-4">
                                <h3 class="text-white font-semibold text-lg mb-1">Resilient Communities</h3>
                                <p class="text-gray-200 text-sm">Building stronger communities together</p>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="bg-white/20 backdrop-blur-sm rounded-full p-2">
                                <i class="fas fa-expand text-white text-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </section>
        <!-- Image Lightbox Modal -->
        <div id="lightbox-modal" class="fixed inset-0 bg-black/90 z-50 hidden items-center justify-center p-4">
            <div class="relative max-w-7xl max-h-full w-full h-full flex items-center justify-center">
                <!-- Close Button -->
                <button id="lightbox-close" class="absolute top-4 right-4 z-60 text-white hover:text-gray-300 transition-colors">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>

                <!-- Previous Button -->
                <button id="lightbox-prev" class="absolute left-4 top-1/2 transform -translate-y-1/2 z-60 text-white hover:text-gray-300 transition-colors">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>

                <!-- Next Button -->
                <button id="lightbox-next" class="absolute right-4 top-1/2 transform -translate-y-1/2 z-60 text-white hover:text-gray-300 transition-colors">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>

                <!-- Image Container -->
                <div class="relative max-w-full max-h-full">
                    <img id="lightbox-image" src="" alt="" class="max-w-full max-h-full object-contain rounded-lg shadow-2xl">

                    <!-- Image Info -->
                    <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6 rounded-b-lg">
                        <h3 id="lightbox-title" class="text-white text-xl font-semibold mb-2"></h3>
                        <p id="lightbox-description" class="text-gray-200"></p>
                        <div class="flex justify-between items-center mt-4">
                            <span id="lightbox-counter" class="text-gray-300 text-sm"></span>
                            <div class="flex space-x-2">
                                <button id="lightbox-download" class="bg-white/20 backdrop-blur-sm text-white px-4 py-2 rounded-lg hover:bg-white/30 transition-colors">
                                    <i class="fas fa-download mr-2"></i>Download
                                </button>
                                <button id="lightbox-share" class="bg-white/20 backdrop-blur-sm text-white px-4 py-2 rounded-lg hover:bg-white/30 transition-colors">
                                    <i class="fas fa-share mr-2"></i>Share
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

     <!-- Fixed Contact Button Code-->
     <section>
        <div id="contact-button" class="fixed bottom-6 right-6 z-50 bg-[#EC2E15]">
            <!-- Contact Options Modal -->
            <div id="contact-options" class="hidden">
                <!-- Modal Background -->
                <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity duration-300"></div>

                <!-- Modal Content -->
                <div
                    class="absolute bottom-20 right-0 w-72 bg-white rounded-xl shadow-2xl transform transition-all duration-300">
                    <!-- Header -->
                    <div class="p-6 border-b border-gray-100">
                        <h3 class="text-xl font-semibold text-gray-800">
                            Let's Chat here! 👋
                        </h3>
                        <p class="text-lg text-gray-600 mt-2">
                            Choose how you'd like to connect with us
                        </p>
                    </div>

                    <!-- Contact Options -->
                    <div class="p-4 space-y-3">
                        <!-- WhatsApp Option -->
                        <a href="https://api.whatsapp.com/send/?phone=2630714397788&amp;text&amp;type=phone_number&amp;app_absent=0"
                            target="_blank"
                            class="flex items-center gap-4 p-3 rounded-lg hover:bg-green-50 transition-all duration-300 group">
                            <div
                                class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white shadow-lg group-hover:scale-110 transition-transform duration-300">
                                <i class="fab fa-whatsapp text-xl"></i>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-800">WhatsApp</h4>
                                <p class="text-lg text-gray-500">
                                    Instant messaging support
                                </p>
                            </div>
                            <i
                                class="fas fa-chevron-right ml-auto text-gray-400 group-hover:translate-x-1 transition-transform duration-300"></i>
                        </a>

                        <!-- Messenger Option -->
                        <a href="https://api.whatsapp.com/send/?phone=2630714397788&amp;text&amp;type=phone_number&amp;app_absent=0"
                            target="_blank"
                            class="flex items-center gap-4 p-3 rounded-lg hover:bg-purple-50 transition-all duration-300 group">
                            <div
                                class="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center text-white shadow-lg group-hover:scale-110 transition-transform duration-300">
                                <i class="fab fa-facebook-messenger text-xl"></i>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-800">Messenger</h4>
                                <p class="text-lg text-gray-500">Chat via Facebook</p>
                            </div>
                            <i
                                class="fas fa-chevron-right ml-auto text-gray-400 group-hover:translate-x-1 transition-transform duration-300"></i>
                        </a>

                        <!-- Phone Option -->
                        <a href="tel:+263786842638"
                            class="flex items-center gap-4 p-3 rounded-lg hover:bg-blue-50 transition-all duration-300 group">
                            <div
                                class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white shadow-lg group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-phone text-xl"></i>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-800">Phone Call</h4>
                                <p class="text-lg text-gray-500">Direct voice support</p>
                            </div>
                            <i
                                class="fas fa-chevron-right ml-auto text-gray-400 group-hover:translate-x-1 transition-transform duration-300"></i>
                        </a>
                    </div>
                </div>
            </div>
            <!-- Main Toggle Button -->
            <button id="toggle-contact"
                class="group bg-gradient-to-r from-indigo-500 to-purple-600 text-white p-4 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center gap-3">
                <i class="fas fa-comments text-xl group-hover:rotate-12 transition-transform duration-300"></i>
                <span class="text-lg font-medium mr-2">Let's Chat</span>
            </button>
        </div>
    </section>
</body>
<!-- Footer Section -->
<footer 
class="text-white py-16 relative bg-cover bg-center bg-no-repeat" 
style="background-image: url('../assets/images/farm3.jpg');"
>
<!-- Dark overlay for readability -->
<div class="absolute inset-0 bg-black/60"></div>

<!-- Content sits above overlay -->
<div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <div>
            <h3 class="text-lg font-semibold mb-4 text-[#CFB01A]">Contact Us</h3>
            <ul class="space-y-2">
                <a href="tel:0786842638" target="_blank">
                    <li class="flex items-center">
                        <i class="fas fa-phone mr-2"></i>
                        <span>+263 77 853 8394</span>
                    </li>
                </a>
                <a href="mailto:<EMAIL>" target="_blank">
                    <li class="flex items-center">
                        <i class="fas fa-envelope mr-2"></i>
                        <span><EMAIL> | <EMAIL></span>
                    </li>
                </a>
                <li class="flex items-center">
                    <i class="fas fa-location-dot mr-2"></i>
                    <span>27 St Patricks Rd, Hatfield, Harare Zimbabwe</span>
                </li>
            </ul>
        </div>

        <div>
            <h3 class="text-lg font-semibold mb-4 text-[#CFB01A]">Quick Links</h3>
            <ul class="space-y-2">
                <li><a href="../index-2.html" class="text-white hover:text-orange-500 transition-colors">Home</a></li>
                <li><a href="about.html" class="hover:text-[#CFB01A] transition-colors">About Us</a></li>
                <li><a href="downloads.html" class="hover:text-[#CFB01A] transition-colors">Documents</a></li>
            </ul>
        </div>

        <div>
            <h3 class="text-lg font-semibold text-[#CFB01A] mb-4">Connect With Us</h3>
            <div class="flex space-x-4">
                <a href="https://web.facebook.com/MinistryofICTPCSZimbabwe" target="_blank"
                    class="hover:text-[#CFB01A] transition-colors" aria-label="Facebook">
                    <i class="fab fa-facebook text-2xl"></i>
                </a>
                <a href="https://x.com/MICTPCS_ZW" target="_blank"
                    class="hover:text-[#CFB01A] transition-colors" aria-label="x">
                    <i class="fa-brands fa-x-twitter text-2xl"></i>
                </a>
                <a href="https://www.linkedin.com/in/ministry-of-ict-postal-and-courier-services-zimbabwe-638721308/"
                    target="_blank" class="hover:text-[#CFB01A] transition-colors" aria-label="LinkedIn">
                    <i class="fab fa-linkedin text-2xl"></i>
                </a>
                <a href="#" class="hover:text-[#CFB01A] transition-colors" aria-label="YouTube">
                    <i class="fab fa-youtube text-2xl"></i>
                </a>
            </div>
        </div>

        <div>
            <h3 class="text-lg font-semibold text-[#CFB01A] mb-4">Mission</h3>
            <p style="text-align: justify;">
                The mission of the Council of Churches is to facilitate the empowerment and renewal of our member churches so that they have a sustainable transforming Christian presence in Zimbabwe.
              </p>
              
        </div>
    </div>

    <div class="mt-12 pt-8 border-t border-[#CFB01A] text-center">
        <p>Developed by Kudakwashe C Marufu</p>
    </div>
</div>
</footer>

<!-- Scripts -->
<script src="../main.js"></script>
<script src="../javascript/projects.js" ></script>

<!-- Mirrored from www.ictministry.gov.zw/pages/projects by HTTrack Website Copier/3.x [XR&CO'2014], Thu, 26 Jun 2025 08:58:12 GMT -->
</html>
