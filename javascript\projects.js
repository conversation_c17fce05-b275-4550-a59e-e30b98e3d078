// Initialize AOS
AOS.init({
    duration: 1000,
    once: true,
    offset: 100
});

// GSAP Animations for hero section
gsap.from(".hero-content", {
    duration: 1.5,
    y: 100,
    opacity: 0,
    ease: "power4.out"
});

// Smooth scroll behavior for the entire page
document.documentElement.style.scrollBehavior = 'smooth';

// Gallery functionality
class GalleryManager {
    constructor() {
        this.currentImageIndex = 0;
        this.currentFilter = 'all';
        this.images = [];
        this.filteredImages = [];
        this.lightboxModal = document.getElementById('lightbox-modal');
        this.lightboxImage = document.getElementById('lightbox-image');
        this.lightboxTitle = document.getElementById('lightbox-title');
        this.lightboxDescription = document.getElementById('lightbox-description');
        this.lightboxCounter = document.getElementById('lightbox-counter');

        this.init();
    }

    init() {
        this.collectImages();
        this.setupFilterButtons();
        this.setupLightbox();
        this.setupKeyboardNavigation();
        this.setupLazyLoading();
        this.setupImageLoadingStates();
        this.setupInteractiveFeatures();
    }

    collectImages() {
        const galleryItems = document.querySelectorAll('.gallery-item');
        this.images = Array.from(galleryItems).map((item, index) => {
            const img = item.querySelector('img');
            const title = item.querySelector('h3')?.textContent || 'Gallery Image';
            const description = item.querySelector('p')?.textContent || '';
            const category = item.dataset.category || 'all';

            return {
                index,
                src: img.src,
                alt: img.alt,
                title,
                description,
                category,
                element: item
            };
        });

        this.filteredImages = [...this.images];
        this.setupImageClickHandlers();
    }

    setupImageClickHandlers() {
        this.images.forEach((imageData, index) => {
            imageData.element.addEventListener('click', () => {
                this.openLightbox(index);
            });
        });
    }

    setupFilterButtons() {
        const filterButtons = document.querySelectorAll('.gallery-filter-btn');

        filterButtons.forEach(button => {
            button.addEventListener('click', () => {
                const filter = button.dataset.filter;
                this.filterImages(filter);
                this.updateActiveFilter(button);
            });
        });
    }

    filterImages(filter) {
        this.currentFilter = filter;
        const galleryItems = document.querySelectorAll('.gallery-item');

        galleryItems.forEach(item => {
            const category = item.dataset.category;

            if (filter === 'all' || category === filter) {
                item.style.display = 'block';
                // Animate in
                gsap.fromTo(item,
                    { opacity: 0, scale: 0.8 },
                    { opacity: 1, scale: 1, duration: 0.5, ease: "power2.out" }
                );
            } else {
                // Animate out
                gsap.to(item, {
                    opacity: 0,
                    scale: 0.8,
                    duration: 0.3,
                    ease: "power2.in",
                    onComplete: () => {
                        item.style.display = 'none';
                    }
                });
            }
        });

        // Update filtered images array for lightbox navigation
        this.filteredImages = this.images.filter(img =>
            filter === 'all' || img.category === filter
        );
    }

    updateActiveFilter(activeButton) {
        document.querySelectorAll('.gallery-filter-btn').forEach(btn => {
            btn.classList.remove('active', 'bg-[#037F06]', 'text-white');
            btn.classList.add('bg-gray-200', 'text-gray-700');
        });

        activeButton.classList.add('active', 'bg-[#037F06]', 'text-white');
        activeButton.classList.remove('bg-gray-200', 'text-gray-700');
    }

    setupLightbox() {
        // Close button
        document.getElementById('lightbox-close').addEventListener('click', () => {
            this.closeLightbox();
        });

        // Previous button
        document.getElementById('lightbox-prev').addEventListener('click', () => {
            this.previousImage();
        });

        // Next button
        document.getElementById('lightbox-next').addEventListener('click', () => {
            this.nextImage();
        });

        // Download button
        document.getElementById('lightbox-download').addEventListener('click', () => {
            this.downloadImage();
        });

        // Share button
        document.getElementById('lightbox-share').addEventListener('click', () => {
            this.shareImage();
        });

        // Close on background click
        this.lightboxModal.addEventListener('click', (e) => {
            if (e.target === this.lightboxModal) {
                this.closeLightbox();
            }
        });
    }

    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            if (!this.lightboxModal.classList.contains('hidden')) {
                switch(e.key) {
                    case 'Escape':
                        this.closeLightbox();
                        break;
                    case 'ArrowLeft':
                        this.previousImage();
                        break;
                    case 'ArrowRight':
                        this.nextImage();
                        break;
                }
            }
        });
    }

    openLightbox(imageIndex) {
        // Find the index in filtered images
        const originalImage = this.images[imageIndex];
        const filteredIndex = this.filteredImages.findIndex(img => img.index === imageIndex);

        if (filteredIndex !== -1) {
            this.currentImageIndex = filteredIndex;
            this.updateLightboxContent();
            this.lightboxModal.classList.remove('hidden');
            this.lightboxModal.classList.add('flex');
            document.body.style.overflow = 'hidden';

            // Animate in
            gsap.fromTo(this.lightboxModal,
                { opacity: 0 },
                { opacity: 1, duration: 0.3 }
            );
            gsap.fromTo(this.lightboxImage,
                { scale: 0.8, opacity: 0 },
                { scale: 1, opacity: 1, duration: 0.5, ease: "power2.out" }
            );
        }
    }

    closeLightbox() {
        gsap.to(this.lightboxModal, {
            opacity: 0,
            duration: 0.3,
            onComplete: () => {
                this.lightboxModal.classList.add('hidden');
                this.lightboxModal.classList.remove('flex');
                document.body.style.overflow = 'auto';
            }
        });
    }

    updateLightboxContent() {
        const currentImage = this.filteredImages[this.currentImageIndex];

        if (currentImage) {
            this.lightboxImage.src = currentImage.src;
            this.lightboxImage.alt = currentImage.alt;
            this.lightboxTitle.textContent = currentImage.title;
            this.lightboxDescription.textContent = currentImage.description;
            this.lightboxCounter.textContent = `${this.currentImageIndex + 1} of ${this.filteredImages.length}`;

            // Animate image change
            gsap.fromTo(this.lightboxImage,
                { opacity: 0.5, scale: 0.95 },
                { opacity: 1, scale: 1, duration: 0.3 }
            );
        }
    }

    previousImage() {
        if (this.filteredImages.length > 1) {
            this.currentImageIndex = (this.currentImageIndex - 1 + this.filteredImages.length) % this.filteredImages.length;
            this.updateLightboxContent();
        }
    }

    nextImage() {
        if (this.filteredImages.length > 1) {
            this.currentImageIndex = (this.currentImageIndex + 1) % this.filteredImages.length;
            this.updateLightboxContent();
        }
    }

    downloadImage() {
        const currentImage = this.filteredImages[this.currentImageIndex];
        if (currentImage) {
            const link = document.createElement('a');
            link.href = currentImage.src;
            link.download = `${currentImage.title.replace(/\s+/g, '_')}.jpg`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }

    shareImage() {
        const currentImage = this.filteredImages[this.currentImageIndex];
        if (currentImage && navigator.share) {
            navigator.share({
                title: currentImage.title,
                text: currentImage.description,
                url: window.location.href
            }).catch(console.error);
        } else {
            // Fallback: copy URL to clipboard
            navigator.clipboard.writeText(window.location.href).then(() => {
                // Show a temporary notification
                this.showNotification('Link copied to clipboard!');
            }).catch(console.error);
        }
    }

    showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
        notification.textContent = message;
        document.body.appendChild(notification);

        gsap.fromTo(notification,
            { opacity: 0, y: -20 },
            { opacity: 1, y: 0, duration: 0.3 }
        );

        setTimeout(() => {
            gsap.to(notification, {
                opacity: 0,
                y: -20,
                duration: 0.3,
                onComplete: () => {
                    document.body.removeChild(notification);
                }
            });
        }, 3000);
    }

    setupLazyLoading() {
        // Create intersection observer for lazy loading
        const lazyImageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    const src = img.dataset.src;

                    if (src) {
                        this.loadImageWithPlaceholder(img, src);
                        lazyImageObserver.unobserve(img);
                    }
                }
            });
        }, {
            rootMargin: '50px 0px',
            threshold: 0.01
        });

        // Observe all gallery images
        document.querySelectorAll('.gallery-item img').forEach(img => {
            // Move src to data-src for lazy loading
            if (img.src && !img.dataset.src) {
                img.dataset.src = img.src;
                img.src = this.createPlaceholderImage();
                img.classList.add('lazy-loading');
            }
            lazyImageObserver.observe(img);
        });
    }

    createPlaceholderImage() {
        // Create a simple placeholder SVG
        const svg = `
            <svg width="400" height="400" xmlns="http://www.w3.org/2000/svg">
                <rect width="100%" height="100%" fill="#f3f4f6"/>
                <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#9ca3af" font-family="Arial, sans-serif" font-size="16">
                    Loading...
                </text>
            </svg>
        `;
        return `data:image/svg+xml;base64,${btoa(svg)}`;
    }

    loadImageWithPlaceholder(img, src) {
        // Create a new image to preload
        const newImg = new Image();

        newImg.onload = () => {
            // Fade out placeholder and fade in real image
            gsap.to(img, {
                opacity: 0.3,
                duration: 0.2,
                onComplete: () => {
                    img.src = src;
                    img.classList.remove('lazy-loading');
                    img.classList.add('lazy-loaded');

                    gsap.to(img, {
                        opacity: 1,
                        duration: 0.3,
                        ease: "power2.out"
                    });
                }
            });
        };

        newImg.onerror = () => {
            // Handle error - show error placeholder
            img.src = this.createErrorPlaceholder();
            img.classList.remove('lazy-loading');
            img.classList.add('lazy-error');
        };

        newImg.src = src;
    }

    createErrorPlaceholder() {
        const svg = `
            <svg width="400" height="400" xmlns="http://www.w3.org/2000/svg">
                <rect width="100%" height="100%" fill="#fee2e2"/>
                <text x="50%" y="45%" text-anchor="middle" dy=".3em" fill="#dc2626" font-family="Arial, sans-serif" font-size="14">
                    Failed to load
                </text>
                <text x="50%" y="55%" text-anchor="middle" dy=".3em" fill="#dc2626" font-family="Arial, sans-serif" font-size="12">
                    Click to retry
                </text>
            </svg>
        `;
        return `data:image/svg+xml;base64,${btoa(svg)}`;
    }

    setupImageLoadingStates() {
        // Add loading skeleton styles
        const style = document.createElement('style');
        style.textContent = `
            .lazy-loading {
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                background-size: 200% 100%;
                animation: loading 1.5s infinite;
            }

            @keyframes loading {
                0% { background-position: 200% 0; }
                100% { background-position: -200% 0; }
            }

            .lazy-loaded {
                animation: fadeIn 0.3s ease-in-out;
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }

            .lazy-error {
                cursor: pointer;
                border: 2px dashed #dc2626;
            }
        `;
        document.head.appendChild(style);

        // Add retry functionality for failed images
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('lazy-error')) {
                const img = e.target;
                const src = img.dataset.src;
                if (src) {
                    img.classList.remove('lazy-error');
                    img.classList.add('lazy-loading');
                    this.loadImageWithPlaceholder(img, src);
                }
            }
        });
    }

    setupInteractiveFeatures() {
        // Enhanced hover effects for gallery items
        document.querySelectorAll('.gallery-item').forEach(item => {
            const img = item.querySelector('img');
            const overlay = item.querySelector('.absolute.inset-0');

            item.addEventListener('mouseenter', () => {
                // Add subtle bounce effect
                gsap.to(item, {
                    y: -5,
                    duration: 0.3,
                    ease: "power2.out"
                });

                // Add glow effect
                gsap.to(item.querySelector('.relative'), {
                    boxShadow: '0 20px 40px rgba(3, 127, 6, 0.3)',
                    duration: 0.3
                });
            });

            item.addEventListener('mouseleave', () => {
                gsap.to(item, {
                    y: 0,
                    duration: 0.3,
                    ease: "power2.out"
                });

                gsap.to(item.querySelector('.relative'), {
                    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
                    duration: 0.3
                });
            });
        });

        // Add parallax effect to hero section
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const heroSection = document.querySelector('section[class*="h-[65vh]"]');

            if (heroSection) {
                const heroImg = heroSection.querySelector('img');
                if (heroImg) {
                    gsap.to(heroImg, {
                        y: scrolled * 0.5,
                        duration: 0.1,
                        ease: "none"
                    });
                }
            }
        });

        // Add stagger animation for filter buttons
        const filterButtons = document.querySelectorAll('.gallery-filter-btn');
        gsap.fromTo(filterButtons,
            { opacity: 0, y: 20 },
            {
                opacity: 1,
                y: 0,
                duration: 0.6,
                stagger: 0.1,
                ease: "power2.out",
                delay: 0.5
            }
        );

        // Add counter animation for statistics
        this.animateCounters();

        // Add image tilt effect on hover
        document.querySelectorAll('.gallery-item img').forEach(img => {
            img.addEventListener('mousemove', (e) => {
                const rect = img.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                const centerX = rect.width / 2;
                const centerY = rect.height / 2;
                const rotateX = (y - centerY) / 10;
                const rotateY = (centerX - x) / 10;

                gsap.to(img, {
                    rotationX: rotateX,
                    rotationY: rotateY,
                    duration: 0.3,
                    ease: "power2.out",
                    transformPerspective: 1000
                });
            });

            img.addEventListener('mouseleave', () => {
                gsap.to(img, {
                    rotationX: 0,
                    rotationY: 0,
                    duration: 0.5,
                    ease: "power2.out"
                });
            });
        });
    }

    animateCounters() {
        const counters = [
            { element: document.getElementById('total-photos'), target: 16 },
            { element: document.getElementById('agriculture-count'), target: 6 },
            { element: document.getElementById('isals-count'), target: 3 },
            { element: document.getElementById('stories-count'), target: 7 }
        ];

        counters.forEach(counter => {
            if (counter.element) {
                gsap.fromTo(counter.element,
                    { textContent: 0 },
                    {
                        textContent: counter.target,
                        duration: 2,
                        ease: "power2.out",
                        snap: { textContent: 1 },
                        delay: 1
                    }
                );
            }
        });
    }
}

// Initialize gallery when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new GalleryManager();
});

// Intersection Observer for scroll animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: "0px"
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('animate-fade-in');
            observer.unobserve(entry.target);
        }
    });
}, observerOptions);

// Observe gallery items for scroll animations
document.addEventListener('DOMContentLoaded', () => {
    document.querySelectorAll('.gallery-item').forEach(item => {
        observer.observe(item);
    });
});
